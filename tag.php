<?php
require('includes/application_top.php');

// Get the tag slug from the URL (e.g., /tag/fathers-day or tag.php?tag=fathers-day)
$tag_slug = isset($_GET['tag']) ? $_GET['tag'] : '';
$tag_name = str_replace('-', ' ', $tag_slug); // Convert slug to tag name



// SEO meta tags
$page_title = 'Products tagged with: ' . htmlspecialchars(ucwords($tag_name));
$meta_description = 'Browse all products tagged with ' . htmlspecialchars($tag_name) . ' at ' . (defined('STORE_NAME') ? STORE_NAME : 'Our Store');

require(DIR_WS_INCLUDES . 'template_top.php');
// Query products with this tag
$listing_sql = "
    SELECT p.products_id, p.products_model, pd.products_name, p.products_image, p.products_price, p.products_tax_class_id
    FROM products p
    JOIN products_tags t ON p.products_id = t.product_id
    JOIN products_description pd ON p.products_id = pd.products_id AND pd.language_id = 1
    WHERE t.tag = '" . tep_db_input($tag_name) . "'
    AND p.products_status = 1
    ORDER BY p.products_date_added DESC
";
?>
<!-- SEO meta tags -->
<?php if (!empty($page_title)) echo '<title>' . $page_title . '</title>'; ?>
<?php if (!empty($meta_description)) echo '<meta name="description" content="' . $meta_description . '">'; ?>

<?php include(DIR_WS_MODULES . FILENAME_PRODUCT_LISTING); ?>

<?php require(DIR_WS_INCLUDES . 'template_bottom.php'); ?>